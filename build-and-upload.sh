#!/bin/bash
echo "=========================================="
echo "   Tixara Build & Upload Script"
echo "=========================================="

# 1. Build locally
echo "[1/4] Building assets locally..."
npm run build

# 2. Create a zip of the built assets
echo "[2/4] Creating zip archive of built assets..."
cd public
zip -r build.zip build

# 3. Upload to cPanel server
echo "[3/4] Uploading to cPanel server..."
# Replace with your server details
SERVER="tixara.pcode.my.id"
USER="dnazcod1"
REMOTE_PATH="/home/<USER>/tixara.pcode.my.id/public"

scp build.zip ${USER}@${SERVER}:${REMOTE_PATH}/

# 4. Extract on server
echo "[4/4] Extracting files on server..."
ssh ${USER}@${SERVER} "cd ${REMOTE_PATH} && unzip -o build.zip && rm build.zip"

echo "=========================================="
echo "   Build and upload completed!"
echo "=========================================="