#!/usr/bin/env node

/**
 * Simplified build script for cPanel environments with memory constraints
 * This script uses esbuild instead of Vite for a more memory-efficient build
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('========================================');
console.log('   Tixara cPanel Memory-Friendly Build');
console.log('========================================');

// Install esbuild if not already installed
try {
  console.log('Checking for esbuild...');
  execSync('npx esbuild --version', { stdio: 'ignore' });
} catch (e) {
  console.log('Installing esbuild (lightweight JS bundler)...');
  execSync('npm install --no-save esbuild', { stdio: 'inherit' });
}

// Create build directory
console.log('Creating build directories...');
const publicDir = path.join(__dirname, 'public');
const buildDir = path.join(publicDir, 'build');

if (!fs.existsSync(buildDir)) {
  fs.mkdirSync(buildDir, { recursive: true });
}

// Build JS files
console.log('Building JS files...');
const jsFiles = [
  'resources/js/app.js',
  'resources/js/live-notifications.js',
  'resources/js/header-functionality.js',
  'resources/js/theme-manager.js'
];

jsFiles.forEach(file => {
  const outputFile = path.join(buildDir, path.basename(file));
  console.log(`Building ${file} -> ${outputFile}`);
  
  try {
    execSync(`npx esbuild ${file} --bundle --minify --outfile=${outputFile}`, {
      stdio: 'inherit',
      env: { ...process.env, NODE_OPTIONS: '--max-old-space-size=512' }
    });
  } catch (e) {
    console.error(`Error building ${file}:`, e.message);
  }
});

// Process CSS with PostCSS
console.log('Processing CSS files...');
try {
  execSync('npm install --no-save postcss postcss-cli autoprefixer tailwindcss', {
    stdio: 'inherit'
  });
  
  // Create minimal postcss config
  const postcssConfig = `
module.exports = {
  plugins: [
    require('tailwindcss'),
    require('autoprefixer')
  ]
}`;
  
  fs.writeFileSync('postcss.config.js', postcssConfig);
  
  // Process main CSS file
  execSync('npx postcss resources/css/app.css -o public/build/app.css', {
    stdio: 'inherit'
  });
} catch (e) {
  console.error('Error processing CSS:', e.message);
}

// Create manifest file
console.log('Creating manifest file...');
const manifest = {
  'resources/css/app.css': {
    file: 'build/app.css',
    isEntry: true
  }
};

jsFiles.forEach(file => {
  manifest[file] = {
    file: `build/${path.basename(file)}`,
    isEntry: true
  };
});

fs.writeFileSync(
  path.join(publicDir, 'build/manifest.json'),
  JSON.stringify(manifest, null, 2)
);

console.log('========================================');
console.log('   Build completed successfully!');
console.log('========================================');
console.log('You can now deploy the built assets.');