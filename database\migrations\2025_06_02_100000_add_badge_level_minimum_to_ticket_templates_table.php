<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ticket_templates', function (Blueprint $table) {
            // Add badge level minimum requirement
            $table->unsignedBigInteger('badge_level_minimum')->nullable();

            // Add foreign key constraint
            $table->foreign('badge_level_minimum')->references('id')->on('user_badge_level')->onDelete('set null');

            // Add index for performance
            $table->index('badge_level_minimum');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ticket_templates', function (Blueprint $table) {
            // Drop foreign key constraint first
            $table->dropForeign(['badge_level_minimum']);
            
            // Drop index
            $table->dropIndex(['badge_level_minimum']);
            
            // Drop column
            $table->dropColumn('badge_level_minimum');
        });
    }
};
