<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class FixCategorySortOrder extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:category-sort-order';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix missing sort_order column in categories table';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Checking categories table structure...');

        if (!Schema::hasTable('categories')) {
            $this->error('Categories table does not exist!');
            return 1;
        }

        if (!Schema::hasColumn('categories', 'sort_order')) {
            $this->info('Adding sort_order column to categories table...');
            
            Schema::table('categories', function ($table) {
                $table->integer('sort_order')->default(0)->after('is_active');
            });
            
            $this->info('Column added successfully.');
        } else {
            $this->info('sort_order column already exists.');
        }

        $this->info('Initializing sort_order values...');
        
        $categories = DB::table('categories')->orderBy('name')->get();
        $bar = $this->output->createProgressBar(count($categories));
        
        foreach ($categories as $index => $category) {
            DB::table('categories')
                ->where('id', $category->id)
                ->update(['sort_order' => ($index + 1) * 10]);
            $bar->advance();
        }
        
        $bar->finish();
        $this->newLine();
        $this->info('Sort order values initialized successfully!');
        
        return 0;
    }
}