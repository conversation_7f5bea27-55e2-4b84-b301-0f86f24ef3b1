<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\TicketTemplate;
use App\Models\UserBadgeLevel;

class TemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $templates = [
            [
                'organizer_id' => null, // Global template
                'name' => 'Classic Boarding Pass',
                'template_type' => 'classic',
                'config' => [
                    'primary_color' => '#059669',
                    'secondary_color' => '#D1D5DB',
                    'background_color' => '#F9FAFB',
                    'text_color' => '#1F2937',
                ],
                'is_active' => true,
                'is_default' => true,
            ],
            [
                'organizer_id' => null,
                'name' => 'Unix Terminal Style',
                'template_type' => 'unix',
                'config' => [
                    'primary_color' => '#00FF00',
                    'secondary_color' => '#333333',
                    'background_color' => '#000000',
                    'text_color' => '#00FF00',
                ],
                'is_active' => true,
                'is_default' => false,
            ],
            [
                'organizer_id' => null,
                'name' => 'Minimal Clean Design',
                'template_type' => 'minimal',
                'config' => [
                    'primary_color' => '#1F2937',
                    'secondary_color' => '#9CA3AF',
                    'background_color' => '#ffffff',
                    'text_color' => '#1F2937',
                ],
                'is_active' => true,
                'is_default' => false,
            ],
            [
                'organizer_id' => null,
                'name' => 'Professional Business',
                'template_type' => 'pro',
                'config' => [
                    'primary_color' => '#1E40AF',
                    'secondary_color' => '#E5E7EB',
                    'background_color' => '#ffffff',
                    'text_color' => '#1F2937',
                    'accent_color' => '#F59E0B',
                ],
                'is_active' => true,
                'is_default' => false,
            ],
        ];

        foreach ($templates as $templateData) {
            TicketTemplate::firstOrCreate(
                ['name' => $templateData['name']],
                $templateData
            );
        }

        $this->command->info('Global ticket templates seeded successfully!');
    }
}
                    'background_color' => '#000000',
                    'primary_color' => '#00FF00',
                    'secondary_color' => '#333333',
                    'text_color' => '#00FF00',
                    'layout' => 'terminal',
                    'elements' => [
                        'terminal_header' => true,
                        'monospace_font' => true,
                        'cursor_blink' => true,
                        'command_prompt' => true
                    ]
                ],
                'config' => [
                    'primary_color' => '#00FF00',
                    'secondary_color' => '#333333',
                    'background_color' => '#000000',
                    'text_color' => '#00FF00',
                ],
                'badge_level_minimum' => $bronze?->id,
                'is_active' => true,
                'is_default' => false,
                'sort_order' => 2,
                'created_by' => 1,
            ],
            [
                'organizer_id' => null,
                'name' => 'Minimal Clean Design',
                'slug' => 'minimal-clean-global',
                'description' => 'Clean and minimalist design focusing on essential information',
                'template_type' => 'minimal',
                'template_data' => [
                    'background_color' => '#ffffff',
                    'primary_color' => '#1F2937',
                    'secondary_color' => '#9CA3AF',
                    'text_color' => '#1F2937',
                    'layout' => 'minimal',
                    'elements' => [
                        'shadows' => false,
                        'borders' => ['style' => 'thin'],
                        'typography' => 'clean',
                        'spacing' => 'generous'
                    ]
                ],
                'config' => [
                    'primary_color' => '#1F2937',
                    'secondary_color' => '#9CA3AF',
                    'background_color' => '#ffffff',
                    'text_color' => '#1F2937',
                ],
                'badge_level_minimum' => $bronze?->id,
                'is_active' => true,
                'is_default' => false,
                'sort_order' => 3,
                'created_by' => 1,
            ],
            [
                'organizer_id' => null,
                'name' => 'Professional Business',
                'slug' => 'professional-business-global',
                'description' => 'Professional design suitable for corporate events and business conferences',
                'template_type' => 'pro',
                'template_data' => [
                    'background_color' => '#ffffff',
                    'primary_color' => '#1E40AF',
                    'secondary_color' => '#E5E7EB',
                    'text_color' => '#1F2937',
                    'accent_color' => '#F59E0B',
                    'layout' => 'corporate',
                    'elements' => [
                        'professional_layout' => true,
                        'company_branding' => true,
                        'formal_typography' => true,
                        'business_colors' => true
                    ]
                ],
                'config' => [
                    'primary_color' => '#1E40AF',
                    'secondary_color' => '#E5E7EB',
                    'background_color' => '#ffffff',
                    'text_color' => '#1F2937',
                    'accent_color' => '#F59E0B',
                ],
                'badge_level_minimum' => $silver?->id,
                'is_active' => true,
                'is_default' => false,
                'sort_order' => 4,
                'created_by' => 1,
            ],
            [
                'organizer_id' => null,
                'name' => 'Custom Canvas Template',
                'slug' => 'custom-canvas-global',
                'description' => 'Fully customizable template with drag-and-drop editor (Platinum users only)',
                'template_type' => 'custom',
                'template_data' => [
                    'background_color' => '#ffffff',
                    'primary_color' => '#6366F1',
                    'secondary_color' => '#F1F5F9',
                    'text_color' => '#0F172A',
                    'accent_color' => '#F59E0B',
                    'layout' => 'custom',
                    'elements' => [
                        'drag_drop_enabled' => true,
                        'custom_elements' => true,
                        'advanced_styling' => true,
                        'canvas_editor' => true
                    ]
                ],
                'config' => [
                    'primary_color' => '#6366F1',
                    'secondary_color' => '#F1F5F9',
                    'background_color' => '#ffffff',
                    'text_color' => '#0F172A',
                    'accent_color' => '#F59E0B',
                ],
                'custom_config' => [
                    'layout' => 'horizontal',
                    'header_style' => 'gradient',
                    'border_style' => 'rounded',
                    'font_family' => 'Inter',
                    'background_pattern' => 'none',
                    'show_price' => true,
                    'show_qr_code' => true,
                    'show_watermark' => true,
                    'show_logo' => true,
                    'editor_enabled' => true,
                ],
                'badge_level_minimum' => $platinum?->id,
                'is_active' => true,
                'is_default' => false,
                'sort_order' => 5,
                'created_by' => 1,
            ],
            [
                'organizer_id' => null,
                'name' => 'Festival Colorful',
                'slug' => 'festival-colorful-global',
                'description' => 'Vibrant and colorful design perfect for music festivals and entertainment events',
                'template_type' => 'classic',
                'template_data' => [
                    'background_color' => '#7C3AED',
                    'primary_color' => '#F59E0B',
                    'secondary_color' => '#EC4899',
                    'text_color' => '#ffffff',
                    'layout' => 'festival',
                    'elements' => [
                        'colorful_gradient' => true,
                        'music_icons' => true,
                        'vibrant_colors' => true,
                        'festival_theme' => true
                    ]
                ],
                'config' => [
                    'primary_color' => '#F59E0B',
                    'secondary_color' => '#EC4899',
                    'background_color' => '#7C3AED',
                    'text_color' => '#ffffff',
                ],
                'badge_level_minimum' => $bronze?->id,
                'is_active' => true,
                'is_default' => false,
                'sort_order' => 6,
                'created_by' => 1,
            ],
            [
                'organizer_id' => null,
                'name' => 'Elegant Dark Theme',
                'slug' => 'elegant-dark-global',
                'description' => 'Sophisticated dark theme with elegant gold accents for premium events',
                'template_type' => 'pro',
                'template_data' => [
                    'background_color' => '#1F2937',
                    'primary_color' => '#F59E0B',
                    'secondary_color' => '#374151',
                    'text_color' => '#ffffff',
                    'layout' => 'elegant',
                    'elements' => [
                        'gradient_background' => true,
                        'gold_accents' => true,
                        'premium_styling' => true,
                        'dark_theme' => true
                    ]
                ],
                'config' => [
                    'primary_color' => '#F59E0B',
                    'secondary_color' => '#374151',
                    'background_color' => '#1F2937',
                    'text_color' => '#ffffff',
                ],
                'badge_level_minimum' => $silver?->id,
                'is_active' => true,
                'is_default' => false,
                'sort_order' => 7,
                'created_by' => 1,
            ]
        ];

        foreach ($templates as $templateData) {
            TicketTemplate::firstOrCreate(
                ['name' => $templateData['name']],
                $templateData
            );
        }

        $this->command->info('Global ticket templates seeded successfully!');
    }
}
