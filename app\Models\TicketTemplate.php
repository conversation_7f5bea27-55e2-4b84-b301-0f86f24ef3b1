<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TicketTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'organizer_id',
        'name',
        'slug',
        'description',
        'template_type',
        'preview_image',
        'template_data',
        'config',
        'custom_config',
        'badge_level_minimum',
        'is_active',
        'is_default',
        'sort_order',
        'created_by',
        'usage_count',
        'last_used_at',
    ];

    protected $casts = [
        'template_data' => 'array',
        'config' => 'array',
        'custom_config' => 'array',
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'last_used_at' => 'datetime',
    ];

    /**
     * Available template types
     */
    const TEMPLATE_TYPES = [
        'classic' => 'Classic',
        'unix' => 'Unix Terminal',
        'minimal' => 'Minimal',
        'pro' => 'Professional',
        'custom' => 'Custom Design',
    ];

    /**
     * Default configuration for each template type
     */
    const DEFAULT_CONFIGS = [
        'classic' => [
            'primary_color' => '#059669',
            'secondary_color' => '#D1D5DB',
            'background_color' => '#F9FAFB',
            'text_color' => '#1F2937',
        ],
        'unix' => [
            'primary_color' => '#00FF00',
            'secondary_color' => '#333333',
            'background_color' => '#000000',
            'text_color' => '#00FF00',
        ],
        'minimal' => [
            'primary_color' => '#2563EB',
            'secondary_color' => '#F8FAFC',
            'background_color' => '#FFFFFF',
            'text_color' => '#1E293B',
        ],
        'pro' => [
            'primary_color' => '#7C3AED',
            'secondary_color' => '#F3F4F6',
            'background_color' => '#FFFFFF',
            'text_color' => '#111827',
            'accent_color' => '#F59E0B',
        ],
        'custom' => [
            'primary_color' => '#6366F1',
            'secondary_color' => '#F1F5F9',
            'background_color' => '#FFFFFF',
            'text_color' => '#0F172A',
            'accent_color' => '#F59E0B',
        ],
    ];

    /**
     * Get events using this template
     */
    public function events()
    {
        return $this->hasMany(Event::class, 'ticket_template_id');
    }

    /**
     * Get creator of template
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Relationship with organizer (user)
     */
    public function organizer()
    {
        return $this->belongsTo(User::class, 'organizer_id');
    }

    /**
     * Relationship with badge level minimum
     */
    public function badgeLevelMinimum()
    {
        return $this->belongsTo(UserBadgeLevel::class, 'badge_level_minimum');
    }

    /**
     * Scope for active templates
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for default template
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Scope for organizer templates
     */
    public function scopeForOrganizer($query, $organizerId)
    {
        return $query->where('organizer_id', $organizerId);
    }

    /**
     * Check if organizer can use custom templates
     */
    public function canUseCustomTemplate()
    {
        return $this->organizer &&
               $this->organizer->badge_level_id >= 4; // Platinum badge or higher
    }

    /**
     * Check if user can access this template based on badge level
     */
    public function canUserAccess(User $user)
    {
        // If no badge level requirement, everyone can access
        if (!$this->badge_level_minimum) {
            return true;
        }

        // If user has no badge level, they can only access templates with no requirement
        if (!$user->badge_level_id) {
            return false;
        }

        // Check if user's badge level meets the minimum requirement
        return $user->badge_level_id >= $this->badge_level_minimum;
    }

    /**
     * Scope for templates accessible by user based on badge level
     */
    public function scopeAccessibleByUser($query, User $user)
    {
        return $query->where(function($q) use ($user) {
            $q->whereNull('badge_level_minimum')
              ->orWhere('badge_level_minimum', '<=', $user->badge_level_id ?? 0);
        });
    }

    /**
     * Get available template types for user based on badge level
     */
    public static function getAvailableTemplateTypes(User $user)
    {
        $allTypes = self::TEMPLATE_TYPES;
        $userBadgeLevel = $user->badge_level_id ?? 1;

        // Define badge requirements for template types
        $typeRequirements = [
            'classic' => 1,  // Bronze
            'unix' => 1,     // Bronze
            'minimal' => 1,  // Bronze
            'pro' => 2,      // Silver
            'custom' => 4,   // Platinum
        ];

        $availableTypes = [];
        foreach ($allTypes as $key => $name) {
            $requiredLevel = $typeRequirements[$key] ?? 1;
            if ($userBadgeLevel >= $requiredLevel) {
                $availableTypes[$key] = $name;
            }
        }

        return $availableTypes;
    }

    /**
     * Get merged configuration (config + custom_config)
     */
    public function getMergedConfig()
    {
        $baseConfig = $this->config ?? self::DEFAULT_CONFIGS[$this->template_type] ?? [];
        $customConfig = $this->custom_config ?? [];

        return array_merge($baseConfig, $customConfig);
    }

    /**
     * Get template view path
     */
    public function getViewPath()
    {
        return "templates.tickets.{$this->template_type}-ticket";
    }

    /**
     * Increment usage count
     */
    public function incrementUsage()
    {
        $this->increment('usage_count');
        $this->update(['last_used_at' => now()]);
    }

    /**
     * Set as default template for organizer
     */
    public function setAsDefault()
    {
        // Remove default from other templates
        static::where('organizer_id', $this->organizer_id)
              ->where('id', '!=', $this->id)
              ->update(['is_default' => false]);

        // Set this as default
        $this->update(['is_default' => true]);
    }

    /**
     * Get template preview URL
     */
    public function getPreviewUrlAttribute()
    {
        if ($this->preview_image) {
            return asset('storage/' . $this->preview_image);
        }

        return asset('images/templates/default-preview.png');
    }

    /**
     * Get template configuration
     */
    public function getConfigAttribute()
    {
        return $this->template_data ?? $this->getDefaultConfig();
    }

    /**
     * Get default template configuration
     */
    private function getDefaultConfig()
    {
        return [
            'background_color' => '#ffffff',
            'primary_color' => '#3B82F6',
            'secondary_color' => '#6B7280',
            'text_color' => '#1F2937',
            'border_radius' => '12px',
            'font_family' => 'Inter, sans-serif',
            'layout' => 'modern',
            'show_qr_code' => true,
            'show_event_image' => true,
            'show_organizer_logo' => true,
            'elements' => [
                'header' => ['show' => true, 'height' => '120px'],
                'event_info' => ['show' => true, 'position' => 'left'],
                'attendee_info' => ['show' => true, 'position' => 'right'],
                'qr_code' => ['show' => true, 'size' => '120px', 'position' => 'bottom-right'],
                'footer' => ['show' => true, 'text' => 'Powered by Tixara']
            ]
        ];
    }

    /**
     * Generate ticket HTML for given data
     */
    public function generateTicketHtml($ticketData)
    {
        $config = $this->config;

        return view('templates.tickets.' . $this->slug, [
            'ticket' => $ticketData,
            'config' => $config,
            'template' => $this
        ])->render();
    }

    /**
     * Get available template layouts
     */
    public static function getAvailableLayouts()
    {
        return [
            'modern' => 'Modern Boarding Pass',
            'classic' => 'Classic Ticket',
            'minimal' => 'Minimal Design',
            'elegant' => 'Elegant Style',
            'festival' => 'Festival Theme',
            'corporate' => 'Corporate Style'
        ];
    }



    /**
     * Create default templates for organizer
     */
    public static function createDefaultTemplatesForOrganizer(User $organizer)
    {
        $availableTypes = self::getAvailableTemplateTypes($organizer);
        $isFirst = true;

        foreach ($availableTypes as $type => $name) {
            self::create([
                'organizer_id' => $organizer->id,
                'name' => $name,
                'slug' => $type . '-' . $organizer->id,
                'template_type' => $type,
                'is_default' => $isFirst, // First template is default
                'is_active' => true,
                'config' => self::DEFAULT_CONFIGS[$type] ?? [],
                'custom_config' => $type === 'custom' ? [
                    'layout' => 'horizontal',
                    'header_style' => 'gradient',
                    'border_style' => 'rounded',
                    'font_family' => 'Inter',
                    'background_pattern' => 'none',
                    'show_price' => true,
                    'show_qr_code' => true,
                    'show_watermark' => true,
                ] : null,
            ]);

            $isFirst = false;
        }
    }

    /**
     * Validate custom configuration
     */
    public function validateCustomConfig(array $customConfig)
    {
        $errors = [];

        // Validate layout
        if (isset($customConfig['layout']) && !in_array($customConfig['layout'], ['horizontal', 'vertical', 'split'])) {
            $errors[] = 'Invalid layout option';
        }

        // Validate header style
        if (isset($customConfig['header_style']) && !in_array($customConfig['header_style'], ['gradient', 'solid', 'image'])) {
            $errors[] = 'Invalid header style option';
        }

        // Validate border style
        if (isset($customConfig['border_style']) && !in_array($customConfig['border_style'], ['rounded', 'sharp', 'dashed'])) {
            $errors[] = 'Invalid border style option';
        }

        // Validate font family
        if (isset($customConfig['font_family']) && !in_array($customConfig['font_family'], ['Inter', 'Poppins', 'Roboto', 'Custom'])) {
            $errors[] = 'Invalid font family option';
        }

        // Validate background pattern
        if (isset($customConfig['background_pattern']) && !in_array($customConfig['background_pattern'], ['none', 'dots', 'lines', 'waves'])) {
            $errors[] = 'Invalid background pattern option';
        }

        // Validate colors (if provided)
        $colorFields = ['primary_color', 'secondary_color', 'background_color', 'text_color', 'accent_color'];
        foreach ($colorFields as $field) {
            if (isset($customConfig[$field]) && !preg_match('/^#[0-9A-Fa-f]{6}$/', $customConfig[$field])) {
                $errors[] = "Invalid color format for {$field}";
            }
        }

        return $errors;
    }

    /**
     * Create default templates
     */
    public static function createDefaults()
    {
        $templates = [
            [
                'name' => 'Modern Boarding Pass',
                'slug' => 'modern-boarding-pass',
                'description' => 'Template modern dengan desain boarding pass yang elegan',
                'template_data' => [
                    'background_color' => '#ffffff',
                    'primary_color' => '#3B82F6',
                    'secondary_color' => '#E5E7EB',
                    'layout' => 'modern',
                    'elements' => [
                        'header' => ['show' => true, 'style' => 'gradient'],
                        'qr_code' => ['show' => true, 'size' => '120px'],
                        'perforated_edge' => true
                    ]
                ],
                'is_active' => true,
                'is_default' => true,
                'sort_order' => 1
            ],
            [
                'name' => 'Classic Ticket',
                'slug' => 'classic-ticket',
                'description' => 'Template klasik dengan desain tiket tradisional',
                'template_data' => [
                    'background_color' => '#F9FAFB',
                    'primary_color' => '#059669',
                    'secondary_color' => '#D1D5DB',
                    'layout' => 'classic',
                    'elements' => [
                        'border' => ['show' => true, 'style' => 'dashed'],
                        'corner_cuts' => true
                    ]
                ],
                'is_active' => true,
                'sort_order' => 2
            ],
            [
                'name' => 'Minimal Design',
                'slug' => 'minimal-design',
                'description' => 'Template minimalis dengan fokus pada informasi penting',
                'template_data' => [
                    'background_color' => '#ffffff',
                    'primary_color' => '#1F2937',
                    'secondary_color' => '#9CA3AF',
                    'layout' => 'minimal',
                    'elements' => [
                        'shadows' => false,
                        'borders' => ['style' => 'thin']
                    ]
                ],
                'is_active' => true,
                'sort_order' => 3
            ],
            [
                'name' => 'Elegant Style',
                'slug' => 'elegant-style',
                'description' => 'Template elegan dengan sentuhan mewah',
                'template_data' => [
                    'background_color' => '#1F2937',
                    'primary_color' => '#F59E0B',
                    'secondary_color' => '#374151',
                    'text_color' => '#ffffff',
                    'layout' => 'elegant',
                    'elements' => [
                        'gradient_background' => true,
                        'gold_accents' => true
                    ]
                ],
                'is_active' => true,
                'sort_order' => 4
            ],
            [
                'name' => 'Festival Theme',
                'slug' => 'festival-theme',
                'description' => 'Template colorful untuk event festival dan musik',
                'template_data' => [
                    'background_color' => '#7C3AED',
                    'primary_color' => '#F59E0B',
                    'secondary_color' => '#EC4899',
                    'text_color' => '#ffffff',
                    'layout' => 'festival',
                    'elements' => [
                        'colorful_gradient' => true,
                        'music_icons' => true
                    ]
                ],
                'is_active' => true,
                'sort_order' => 5
            ],
            [
                'name' => 'Corporate Style',
                'slug' => 'corporate-style',
                'description' => 'Template profesional untuk event corporate',
                'template_data' => [
                    'background_color' => '#ffffff',
                    'primary_color' => '#1E40AF',
                    'secondary_color' => '#E5E7EB',
                    'layout' => 'corporate',
                    'elements' => [
                        'professional_layout' => true,
                        'company_branding' => true
                    ]
                ],
                'is_active' => true,
                'sort_order' => 6
            ]
        ];

        foreach ($templates as $templateData) {
            self::firstOrCreate(
                ['slug' => $templateData['slug']],
                $templateData
            );
        }
    }
}
